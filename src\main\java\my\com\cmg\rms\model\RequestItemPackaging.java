package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_request_item_packaging")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestItemPackaging extends BaseEntity {

  @Id
  @Column(name = "item_packaging_req_seqno", unique = true, nullable = false)
  @SequenceGenerator(
      name = "request_item_packaging_seq_no",
      sequenceName = "rm_request_item_packaging_seqno",
      allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_item_packaging_seq_no")
  private Long itemPackagingReqSeqno;

  @Column(name = "item_packaging_req_code", length = 50)
  private String itemPackagingReqCode;

  @Column(name = "item_name", length = 100)
  private String itemName;

  @Column(name = "item_seqno", length = 100)
  private Long itemSeqno;

  @Column(name = "item_code", length = 100)
  private String itemCode;

  @Column(name = "item_packaging_name", length = 100)
  private String itemPackagingName;

  @Column(name = "sku_seqno", length = 100)
  private Long skuSeqno;

  @Column(name = "sku_abbr", length = 100)
  private String skuAbbr;

  @Column(name = "pku_abbr", length = 100)
  private String pkuAbbr;

  @Column(name = "pku_seqno", length = 100)
  private Long pkuSeqno;

  @Column(name = "conversion_factor", length = 10)
  private BigDecimal conversionFactor;

  @Column(name = "packaging_desc", length = 100)
  private String packagingDesc;

  @Column(name = "product_list", length = 100)
  private String productList;

  @Column(name = "product_seqno", length = 100)
  private Long productSeqno;

  @Column(name = "product_name", length = 100)
  private String productName;

  @Column(name = "manufactured_name", length = 100)
  private String manufacturedName;

  @Column(name = "importer_name", length = 100)
  private String importerName;

  @Column(name = "manufactured_address", length = 100)
  private String manufacturedAddress;

  @Column(name = "importer_address", length = 100)
  private String importerAddress;

  @Column(name = "gtin_no", length = 100)
  private String gtinNo;

  @Column(name = "mda_no", length = 100)
  private String mdaNo;
}