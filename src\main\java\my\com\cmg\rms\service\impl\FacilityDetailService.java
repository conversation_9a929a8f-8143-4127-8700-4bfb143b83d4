package my.com.cmg.rms.service.impl;

import jakarta.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.RequestDetail.FacilityDetail.FacilityDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.FacilityDetail.FacilityDetailRequestDTO;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;
import my.com.cmg.rms.model.RequestFacility;
import my.com.cmg.rms.repository.jooq.FacilityDetailRepositoryJooq;
import my.com.cmg.rms.repository.jpa.FacilityDetailRepository;
import my.com.cmg.rms.service.IFacilityDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class FacilityDetailService implements IFacilityDetailService {

  private final FacilityDetailRepository requestFacilityRepository;
  private final FacilityDetailRepositoryJooq facilityDetailRepositoryJooq;

  public FacilityDetailService(
      final FacilityDetailRepository requestFacilityRepository,
      final FacilityDetailRepositoryJooq facilityDetailRepositoryJooq) {
    this.requestFacilityRepository = requestFacilityRepository;
    this.facilityDetailRepositoryJooq = facilityDetailRepositoryJooq;
  }

  @Override
  public FacilityDetailDTO getFacilityDetail(Long facilityReqSeqno) {
    return facilityDetailRepositoryJooq.getFacilityDetail(facilityReqSeqno);
  }

  @Override
  @Transactional
  public Long createFacility(FacilityDetailRequestDTO facilityRequestDTO) {

    RequestFacility facility = new RequestFacility();

    updateFacilityFromDTO(facility, facilityRequestDTO);

    facility.setCreatedBy(1L);
    facility.setUpdatedBy(1L);
    facility.setCreatedDate(LocalDateTime.now());
    facility.setUpdatedDate(LocalDateTime.now());
    facility.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    RequestFacility savedFacility = requestFacilityRepository.save(facility);

    return savedFacility.getFacilityReqSeqno();
  }

  @Override
  @Transactional
  public Long save(FacilityDetailRequestDTO facilityRequestDTO) {
    if (facilityRequestDTO.facilityReqSeqno() == null) {
      throw new RmsException(ExceptionCode.BAD_REQUEST, "facilityReqSeqno is required for update");
    }

    RequestFacility facility =
        requestFacilityRepository
            .findById(facilityRequestDTO.facilityReqSeqno())
            .orElseThrow(() -> new EntityNotFoundException("Facility not found"));

    updateFacilityFromDTO(facility, facilityRequestDTO);
    RequestFacility savedFacility = requestFacilityRepository.save(facility);
    return savedFacility.getFacilityReqSeqno();
  }

  private void updateFacilityFromDTO(RequestFacility facility, FacilityDetailRequestDTO dto) {
    facility.setFacilityReqName(dto.facilityReqName());
    facility.setUpdatedDate(LocalDateTime.now());
    facility.setUpdatedBy(1L);
    facility.setFacilityReqCode(dto.facilityReqName());
    facility.setFacilityReqGroup(dto.facilityReqGroup());
    facility.setMinistry(dto.ministry());
    facility.setFacilityReqCategory(dto.facilityReqCategory());
    facility.setFacilityReqType(dto.facilityReqType());
    facility.setAddress1(dto.address1());
    facility.setAddress2(dto.address2());
    facility.setAddress3(dto.address3());
    facility.setCity(dto.city());
    facility.setPostcode(dto.postcode());
    facility.setCountry(dto.country());
    facility.setMobilePhone(dto.mobilePhone());
    facility.setEmail(dto.email());
    facility.setContactPerson(dto.contactPerson());
    facility.setContactNo(dto.contactNo());
  }
}
