package my.com.cmg.rms.service.impl;

import jakarta.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.RequestDetail.SupplierDetail.SupplierDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.SupplierDetail.SupplierDetailRequestDTO;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;
import my.com.cmg.rms.model.RequestSupplier;
import my.com.cmg.rms.repository.jooq.SupplierDetailRepositoryJooq;
import my.com.cmg.rms.repository.jpa.SupplierDetailRepository;
import my.com.cmg.rms.service.ISupplierDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SupplierDetailService implements ISupplierDetailService {

  private final SupplierDetailRepository requestSupplierRepository;
  private final SupplierDetailRepositoryJooq supplierDetailRepositoryJooq;

  public SupplierDetailService(
      final SupplierDetailRepository requestSupplierRepository,
      final SupplierDetailRepositoryJooq supplierDetailRepositoryJooq) {
    this.requestSupplierRepository = requestSupplierRepository;
    this.supplierDetailRepositoryJooq = supplierDetailRepositoryJooq;
  }

  @Override
  public SupplierDetailDTO getSupplierDetail(Long supplierReqSeqno) {
    return supplierDetailRepositoryJooq.getSupplierDetail(supplierReqSeqno);
  }

  @Override
  @Transactional
  public Long createSupplier(SupplierDetailRequestDTO supplierRequestDTO) {

    RequestSupplier supplier = new RequestSupplier();

    updateSupplierFromDTO(supplier, supplierRequestDTO);

    supplier.setCreatedBy(1L);
    supplier.setUpdatedBy(1L);
    supplier.setCreatedDate(LocalDateTime.now());
    supplier.setUpdatedDate(LocalDateTime.now());
    supplier.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    RequestSupplier savedSupplier = requestSupplierRepository.save(supplier);

    return savedSupplier.getSupplierReqSeqno();
  }

  @Override
  @Transactional
  public Long save(SupplierDetailRequestDTO supplierRequestDTO) {
    if (supplierRequestDTO.supplierReqSeqno() == null) {
      throw new RmsException(ExceptionCode.BAD_REQUEST, "supplierReqSeqno is required for update");
    }

    RequestSupplier supplier =
        requestSupplierRepository
            .findById(supplierRequestDTO.supplierReqSeqno())
            .orElseThrow(() -> new EntityNotFoundException("Supplier not found"));

    updateSupplierFromDTO(supplier, supplierRequestDTO);
    RequestSupplier savedSupplier = requestSupplierRepository.save(supplier);
    return savedSupplier.getSupplierReqSeqno();
  }

  private void updateSupplierFromDTO(RequestSupplier supplier, SupplierDetailRequestDTO dto) {
    supplier.setSupplierReqName(dto.supplierReqName());
    supplier.setCompanyRegNo(dto.companyRegNo());
    supplier.setRegExpiryDate(dto.regExpiryDate().atStartOfDay());

    supplier.setUpdatedDate(LocalDateTime.now());
    supplier.setUpdatedBy(1L);
    supplier.setSupplierReqCode(dto.supplierReqName());

    supplier.setTrsRegNo(dto.trsRegNo());
    supplier.setCompanyStatus(dto.companyStatus()); // make sure this is value
    supplier.setAddress1(dto.address1());
    supplier.setAddress2(dto.address2());
    supplier.setAddress3(dto.address3());
    supplier.setCity(dto.city());
    supplier.setPostcode(dto.postcode());
    supplier.setState(dto.state()); // make sure this is value
    supplier.setCountry(dto.country());
    supplier.setMobilePhone(dto.mobilePhone());
    supplier.setEmail(dto.email());
    supplier.setContactPerson(dto.contactPerson());
    supplier.setContactNo(dto.contactNo());
  }
}
