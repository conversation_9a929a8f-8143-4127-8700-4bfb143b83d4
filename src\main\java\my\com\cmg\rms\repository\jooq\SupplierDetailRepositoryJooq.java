package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;

import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDetail.SupplierDetail.SupplierDetailDTO;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record17;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class SupplierDetailRepositoryJooq {

  private final DSLContext dsl;

  public SupplierDetailRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  /** GET supplier detail by supplier request sequence number */
  public SupplierDetailDTO getSupplierDetail(Long transSeqno) {

    // condition
    Condition condition = noCondition();
    condition = condition.and(field("supplier_req_seqno").eq(transSeqno));

    // fields from SUPPLIER
    Field<Long> supplierReqSeqno = field("supplier_req_seqno", Long.class).as("supplierReqSeqno");
    Field<String> supplierReqName = field("supplier_req_name", String.class).as("supplierReqName");
    Field<String> companyRegNo = field("company_reg_no", String.class).as("companyRegNo");
    Field<LocalDate> regExpiryDate = field("reg_expiry_date", LocalDate.class).as("regExpiryDate");
    Field<String> trsRegNo = field("trs_reg_no", String.class).as("trsRegNo");
    Field<String> companyStatus = field("company_status", String.class).as("companyStatus");
    Field<String> address1 = field("address1", String.class).as("address1");
    Field<String> address2 = field("address2", String.class).as("address2");
    Field<String> address3 = field("address3", String.class).as("address3");
    Field<String> city = field("city", String.class).as("city");
    Field<String> postcode = field("postcode", String.class).as("postcode");
    Field<String> state = field("state", String.class).as("state");
    Field<String> country = field("country", String.class).as("country");
    Field<String> mobilePhone = field("mobile_phone", String.class).as("mobilePhone");
    Field<String> email = field("email", String.class).as("email");
    Field<String> contactPerson = field("contact_person", String.class).as("contactPerson");
    Field<String> contactNo = field("contact_no", String.class).as("contactNo");

    Select<
            Record17<
                Long,
                String,
                String,
                LocalDate,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String>>
        query =
            dsl.select(
                    supplierReqSeqno,
                    supplierReqName,
                    companyRegNo,
                    regExpiryDate,
                    trsRegNo,
                    companyStatus,
                    address1,
                    address2,
                    address3,
                    city,
                    postcode,
                    state,
                    country,
                    mobilePhone,
                    email,
                    contactPerson,
                    contactNo)
                .from(TableUtil.table(TableUtil.RM_REQUEST_SUPPLIER, "SUP"))
                .where(condition)
                .limit(1);

    log.info(LogUtil.QUERY, query);
    SupplierDetailDTO result = query.fetchOneInto(SupplierDetailDTO.class);

    return result;
  }
}
