package my.com.cmg.rms.dto.RequestDetail.ItemMasterDetail;

import java.time.LocalDateTime;

public record ItemMasterDetailRequestDTO(
    Long itemReqSeqno,
    String itemGroupCode,
    Long genericNameSeqno,
    String otherActiveIngredient,
    String strength,
    Long dosageSeqno,
    String itemName,
    Long itemCatSeqno,
    Long itemSubgroupSeqno,
    Long freqSeqno,
    String administrationRoute,
    String drugIndication,
    String rpItemTypeCode,
    Long itemPackagingSeqno,
    Long skuSeqno,
    Long pkuSeqno,
    String conversionFactor,
    String packagingDesc,
    String mdcNo,
    Long productSeqno,
    String productName,
    String manufacturedName,
    String importerName,
    String manufacturedAddress,
    String importerAddress,
    String gtinNo,
    String mdaNo,
    Long createdBy,
    Long updatedBy,
    LocalDateTime CreatedDate,
    LocalDateTime UpdatedDate,
    String ActiveFlag) {}
