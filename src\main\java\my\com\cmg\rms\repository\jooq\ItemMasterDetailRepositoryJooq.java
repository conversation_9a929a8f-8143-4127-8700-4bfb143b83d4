package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;

import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDetail.ItemMasterDetail.ItemMasterDetailDTO;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class ItemMasterDetailRepositoryJooq {
  DSLContext dsl;

  public ItemMasterDetailRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  public ItemMasterDetailDTO getItemMasterDetail(Long transSeqno) {

    // condition:
    Condition condition = noCondition();
    condition = condition.and(field("item_req_seqno").eq(transSeqno));

    // fields from ITEMS
    Field<Long> itemReqSeqno = field("item_req_seqno", Long.class).as("itemReqSeqno");
    Field<String> itemGroupCode = field("item_group_code", String.class).as("itemGroupCode");
    Field<Long> genericNameSeqno = field("generic_name_seqno", Long.class).as("genericNameSeqno");
    Field<String> otherActiveIngredient =
        field("other_active_ingredient", String.class).as("otherActiveIngredient");
    Field<String> strength = field("strength", String.class).as("strength");
    Field<Long> dosageSeqno = field("dosage_seqno", Long.class).as("dosageSeqno");
    Field<String> itemName = field("item_name", String.class).as("itemName");
    Field<Long> itemCatSeqno = field("itm_cat_seqno", Long.class).as("itemCatSeqno");
    Field<Long> itemSubgroupSeqno = field("itm_subgroup_seqno", Long.class).as("itemSubgroupSeqno");
    Field<Long> freqSeqno = field("freq_seqno", Long.class).as("freqSeqno");
    Field<String> administrationRoute =
        field("administration_route", String.class).as("administrationRoute");
    Field<String> drugIndication = field("drug_indication", String.class).as("drugIndication");
    Field<String> rpItemTypeCode = field("rp_item_type_code", String.class).as("rpItemTypeCode");
    Field<Long> itemPackagingSeqno =
        field("item_packaging_seqno", Long.class).as("itemPackagingSeqno");
    Field<Long> skuSeqno = field("sku_seqno", Long.class).as("skuSeqno");
    Field<Long> pkuSeqno = field("pku_seqno", Long.class).as("pkuSeqno");
    Field<BigDecimal> conversionFactorNum =
        field("conversion_factor_num", BigDecimal.class).as("conversionFactorNum");
    Field<String> packagingDesc = field("packaging_desc", String.class).as("packagingDesc");
    Field<String> mdcNo = field("mdc_no", String.class).as("mdcNo");
    Field<Long> productSeqno = field("product_seqno", Long.class).as("productSeqno");
    Field<String> productName = field("product_name", String.class).as("productName");
    Field<String> manufacturedName =
        field("manufactured_name", String.class).as("manufacturedName");
    Field<String> importerName = field("importer_name", String.class).as("importerName");
    Field<String> manufacturedAddress =
        field("manufactured_address", String.class).as("manufacturedAddress");
    Field<String> importerAddress = field("importer_address", String.class).as("importerAddress");
    Field<String> gtinNo = field("gtin_no", String.class).as("gtinNo");
    Field<String> mdaNo = field("mda_no", String.class).as("mdaNo");

    var query =
        dsl.select(
                itemReqSeqno,
                itemGroupCode,
                genericNameSeqno,
                otherActiveIngredient,
                strength,
                dosageSeqno,
                itemName,
                itemCatSeqno,
                itemSubgroupSeqno,
                freqSeqno,
                administrationRoute,
                drugIndication,
                rpItemTypeCode,
                itemPackagingSeqno,
                skuSeqno,
                pkuSeqno,
                conversionFactorNum,
                packagingDesc,
                mdcNo,
                productSeqno,
                productName,
                manufacturedName,
                importerName,
                manufacturedAddress,
                importerAddress,
                gtinNo,
                mdaNo)
            .from(TableUtil.table(TableUtil.RM_REQUEST_ITEMS, "ITM"))
            .where(condition)
            .limit(1);

    log.info(LogUtil.QUERY, query);

    ItemMasterDetailDTO result = query.fetchOneInto(ItemMasterDetailDTO.class);

    return result;
  }
}
