package my.com.cmg.rms.service;

import my.com.cmg.rms.dto.RequestDetail.FacilityDetail.FacilityDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.FacilityDetail.FacilityDetailRequestDTO;

public interface IFacilityDetailService {

  FacilityDetailDTO getFacilityDetail(Long facilityReqSeqno);

  Long save(FacilityDetailRequestDTO facilityRequestDTO);

  Long createFacility(FacilityDetailRequestDTO facilityRequestDTO);
}