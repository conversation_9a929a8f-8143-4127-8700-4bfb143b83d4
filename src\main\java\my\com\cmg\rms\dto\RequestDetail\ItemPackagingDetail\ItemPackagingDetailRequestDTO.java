package my.com.cmg.rms.dto.RequestDetail.ItemPackagingDetail;

import java.math.BigDecimal;

public record ItemPackagingDetailRequestDTO(
    Long itemPackagingReqSeqno,
    String itemPackagingReqCode,
    String itemName,
    Long itemSeqno,
    String itemCode,
    String itemPackagingName,
    Long skuSeqno,
    String skuAbbr,
    Long pkuSeqno,
    String pkuAbbr,
    BigDecimal conversionFactor,
    String packagingDesc,
    String productList,
    Long productSeqno,
    String productName,
    String manufacturedName,
    String importerName,
    String manufacturedAddress,
    String importerAddress,
    String gtinNo,
    String mdaNo,
    Long createdBy,
    Long updatedBy) {}
