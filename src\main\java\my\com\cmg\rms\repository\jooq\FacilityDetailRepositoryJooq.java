package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;

import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDetail.FacilityDetail.FacilityDetailDTO;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record17;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class FacilityDetailRepositoryJooq {

  private final DSLContext dsl;

  public FacilityDetailRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  public FacilityDetailDTO getFacilityDetail(Long transSeqno) {

    // condition
    Condition condition = noCondition();
    condition = condition.and(field("facility_req_seqno").eq(transSeqno));

    // fields from FACILITY
    Field<Long> facilityReqSeqno = field("facility_req_seqno", Long.class).as("facilityReqSeqno");
    Field<String> facilityReqName = field("facility_req_name", String.class).as("facilityReqName");
    Field<String> facilityReqGroup =
        field("facility_req_group", String.class).as("facilityReqGroup");
    Field<String> ministry = field("ministry", String.class).as("ministry");
    Field<String> facilityReqCategory =
        field("facility_req_category", String.class).as("facilityReqCategory");
    Field<String> facilityReqType = field("facility_req_type", String.class).as("facilityReqType");
    Field<String> address1 = field("address1", String.class).as("address1");
    Field<String> address2 = field("address2", String.class).as("address2");
    Field<String> address3 = field("address3", String.class).as("address3");
    Field<String> city = field("city", String.class).as("city");
    Field<String> postcode = field("postcode", String.class).as("postcode");
    Field<String> state = field("state", String.class).as("state");
    Field<String> country = field("country", String.class).as("country");
    Field<String> mobilePhone = field("mobile_phone", String.class).as("mobilePhone");
    Field<String> email = field("email", String.class).as("email");
    Field<String> contactPerson = field("contact_person", String.class).as("contactPerson");
    Field<String> contactNo = field("contact_no", String.class).as("contactNo");

    Select<
            Record17<
                Long,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String,
                String>>
        query =
            dsl.select(
                    facilityReqSeqno,
                    facilityReqName,
                    facilityReqGroup,
                    ministry,
                    facilityReqCategory,
                    facilityReqType,
                    address1,
                    address2,
                    address3,
                    city,
                    postcode,
                    state,
                    country,
                    mobilePhone,
                    email,
                    contactPerson,
                    contactNo)
                .from(TableUtil.table(TableUtil.RM_REQUEST_FACILITY, "FAC"))
                .where(condition)
                .limit(1);

    log.info(LogUtil.QUERY, query);
    FacilityDetailDTO result = query.fetchOneInto(FacilityDetailDTO.class);

    return result;
  }
}
