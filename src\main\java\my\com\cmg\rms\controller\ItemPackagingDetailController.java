package my.com.cmg.rms.controller;

import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDetail.ItemPackagingDetail.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.ItemPackagingDetail.ItemPackagingDetailRequestDTO;
import my.com.cmg.rms.service.IItemPackagingDetailService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/rms/item_packaging_detail")
public class ItemPackagingDetailController {
  private final IItemPackagingDetailService itemPackDetailService;

  public ItemPackagingDetailController(final IItemPackagingDetailService itemPackDetailService) {
    this.itemPackDetailService = itemPackDetailService;
  }

  /**
   * Retrieves the details for the given item packaging request sequence number.
   *
   * @param itemPackagingReqSeqno The item packaging request sequence number.
   * @return The item packaging details as a ItemPackagingDetailDTO
   */
  @GetMapping("/detail/{itemPackagingReqSeqno}")
  public ItemPackagingDetailDTO getItemPackagingDetail(
      @PathVariable("itemPackagingReqSeqno") Long itemPackagingReqSeqno) {
    ItemPackagingDetailDTO response =
        itemPackDetailService.getItemPackagingDetail(itemPackagingReqSeqno);

    return response;
  }

  /**
   * Save the details for the given item packaging request sequence number.
   *
   * @param itemPackagingReqSeqno The item packaging request sequence number.
   * @return The item packaging details as a ItemPackagingDetailRequestDTO
   */
  @PostMapping("/save")
  public Long saveItemPack(@RequestBody ItemPackagingDetailRequestDTO itemPackagingReqSeqno) {
    log.info("Received: {}", itemPackagingReqSeqno);
    ItemPackagingDetailRequestDTO dto = itemPackagingReqSeqno;
    Long response = itemPackDetailService.save(dto);
    return response;
  }

  /**
   * Creates the details for the given item packaging request sequence number.
   *
   * @param itemPackagingReqSeqno The item packaging request sequence number.
   * @return The item packaging details as a ItemPackagingDetailRequestDTO
   */
  @PostMapping("/create")
  public Long createItemPack(@RequestBody ItemPackagingDetailRequestDTO itemPackagingReqSeqno) {
    log.info("Received: {}", itemPackagingReqSeqno);
    Long response = itemPackDetailService.createItemPackaging(itemPackagingReqSeqno);
    return response;
  }
}