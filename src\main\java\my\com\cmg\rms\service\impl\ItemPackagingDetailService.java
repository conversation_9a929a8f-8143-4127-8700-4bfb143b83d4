package my.com.cmg.rms.service.impl;

import jakarta.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.RequestDetail.ItemPackagingDetail.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.ItemPackagingDetail.ItemPackagingDetailRequestDTO;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;
import my.com.cmg.rms.model.RequestItemPackaging;
import my.com.cmg.rms.repository.jooq.ItemPackagingDetailRepositoryJooq;
import my.com.cmg.rms.repository.jpa.ItemPackagingDetailRepository;
import my.com.cmg.rms.service.IItemPackagingDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ItemPackagingDetailService implements IItemPackagingDetailService {

  private final ItemPackagingDetailRepository requestItemPackagingRepository;
  private final ItemPackagingDetailRepositoryJooq itemPackagingDetailRepositoryJooq;

  public ItemPackagingDetailService(
      final ItemPackagingDetailRepository requestItemPackagingRepository,
      final ItemPackagingDetailRepositoryJooq itemPackagingDetailRepositoryJooq) {
    this.requestItemPackagingRepository = requestItemPackagingRepository;
    this.itemPackagingDetailRepositoryJooq = itemPackagingDetailRepositoryJooq;
  }

  @Override
  public ItemPackagingDetailDTO getItemPackagingDetail(Long itemPackagingReqSeqno) {
    return itemPackagingDetailRepositoryJooq.getItemPackagingDetail(itemPackagingReqSeqno);
  }

  @Override
  @Transactional
  public Long createItemPackaging(ItemPackagingDetailRequestDTO itemPackagingDetailRequestDTO) {

    RequestItemPackaging itemPackaging = new RequestItemPackaging();

    updateItemPackagingFromDTO(itemPackaging, itemPackagingDetailRequestDTO);

    itemPackaging.setItemPackagingReqSeqno(itemPackagingDetailRequestDTO.itemPackagingReqSeqno());
    itemPackaging.setItemPackagingReqCode(itemPackagingDetailRequestDTO.itemPackagingReqCode());
    itemPackaging.setItemName(itemPackagingDetailRequestDTO.itemName());
    itemPackaging.setItemSeqno(itemPackagingDetailRequestDTO.itemSeqno());
    itemPackaging.setItemCode(itemPackagingDetailRequestDTO.itemCode());
    itemPackaging.setItemPackagingName(itemPackagingDetailRequestDTO.itemPackagingName());
    itemPackaging.setSkuSeqno(itemPackagingDetailRequestDTO.skuSeqno());
    itemPackaging.setSkuAbbr(itemPackagingDetailRequestDTO.skuAbbr());
    itemPackaging.setPkuAbbr(itemPackagingDetailRequestDTO.pkuAbbr());
    itemPackaging.setPkuSeqno(itemPackagingDetailRequestDTO.pkuSeqno());
    itemPackaging.setConversionFactor(itemPackagingDetailRequestDTO.conversionFactor());
    itemPackaging.setPackagingDesc(itemPackagingDetailRequestDTO.packagingDesc());
    itemPackaging.setProductList(itemPackagingDetailRequestDTO.productList());
    itemPackaging.setProductSeqno(itemPackagingDetailRequestDTO.productSeqno());
    itemPackaging.setProductName(itemPackagingDetailRequestDTO.productName());
    itemPackaging.setManufacturedName(itemPackagingDetailRequestDTO.manufacturedName());
    itemPackaging.setImporterName(itemPackagingDetailRequestDTO.importerName());
    itemPackaging.setManufacturedAddress(itemPackagingDetailRequestDTO.manufacturedAddress());
    itemPackaging.setImporterAddress(itemPackagingDetailRequestDTO.importerAddress());
    itemPackaging.setGtinNo(itemPackagingDetailRequestDTO.gtinNo());
    itemPackaging.setMdaNo(itemPackagingDetailRequestDTO.mdaNo());

    itemPackaging.setCreatedBy(1L);
    itemPackaging.setUpdatedBy(1L);
    itemPackaging.setCreatedDate(LocalDateTime.now());
    itemPackaging.setUpdatedDate(LocalDateTime.now());
    itemPackaging.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    RequestItemPackaging savedItemPackaging = requestItemPackagingRepository.save(itemPackaging);

    return savedItemPackaging.getItemPackagingReqSeqno();
  }

  @Override
  @Transactional
  public Long save(ItemPackagingDetailRequestDTO itemPackagingDetailRequestDTO) {
    if (itemPackagingDetailRequestDTO.itemPackagingReqSeqno() == null) {
      throw new RmsException(
          ExceptionCode.BAD_REQUEST, "itemPackagingReqSeqno is required for update");
    }

    RequestItemPackaging itemPackaging =
        requestItemPackagingRepository
            .findById(itemPackagingDetailRequestDTO.itemPackagingReqSeqno())
            .orElseThrow(() -> new EntityNotFoundException("Item packaging not found"));

    updateItemPackagingFromDTO(itemPackaging, itemPackagingDetailRequestDTO);
    RequestItemPackaging savedItemPackaging = requestItemPackagingRepository.save(itemPackaging);
    return savedItemPackaging.getItemPackagingReqSeqno();
  }

  private void updateItemPackagingFromDTO(
      RequestItemPackaging itemPackaging, ItemPackagingDetailRequestDTO dto) {
    itemPackaging.setItemPackagingReqCode(dto.itemPackagingReqCode());
    itemPackaging.setItemName(dto.itemName());
    itemPackaging.setItemSeqno(dto.itemSeqno());
    itemPackaging.setItemCode(dto.itemCode());
    itemPackaging.setItemPackagingName(dto.itemPackagingName());
    itemPackaging.setSkuSeqno(dto.skuSeqno());
    itemPackaging.setSkuAbbr(dto.skuAbbr());
    itemPackaging.setPkuAbbr(dto.pkuAbbr());
    itemPackaging.setPkuSeqno(dto.pkuSeqno());
    itemPackaging.setConversionFactor(dto.conversionFactor());
    itemPackaging.setPackagingDesc(dto.packagingDesc());
    itemPackaging.setProductList(dto.productList());
    itemPackaging.setProductSeqno(dto.productSeqno());
    itemPackaging.setProductName(dto.productName());
    itemPackaging.setManufacturedName(dto.manufacturedName());
    itemPackaging.setImporterName(dto.importerName());
    itemPackaging.setManufacturedAddress(dto.manufacturedAddress());
    itemPackaging.setImporterAddress(dto.importerAddress());
    itemPackaging.setGtinNo(dto.gtinNo());
    itemPackaging.setMdaNo(dto.mdaNo());
  }
}
