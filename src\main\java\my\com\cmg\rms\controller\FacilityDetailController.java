package my.com.cmg.rms.controller;

import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDetail.FacilityDetail.FacilityDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.FacilityDetail.FacilityDetailRequestDTO;
import my.com.cmg.rms.service.IFacilityDetailService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/rms/facility_detail")
public class FacilityDetailController {
  private final IFacilityDetailService facilityDetailService;

  public FacilityDetailController(final IFacilityDetailService facilityDetailService) {
    this.facilityDetailService = facilityDetailService;
  }

  /**
   * Retrieves the details for the given facility request sequence number.
   *
   * @param facilityReqSeqno The facility request sequence number.
   * @return The facility details as a FacilityDetailDTO
   */
  @GetMapping("/detail/{facilityReqSeqno}")
  public FacilityDetailDTO getFacilityDetail(
      @PathVariable("facilityReqSeqno") Long facilityReqSeqno) {
    FacilityDetailDTO response = facilityDetailService.getFacilityDetail(facilityReqSeqno);

    return response;
  }

  /**
   * Save the details for the given facility request sequence number.
   *
   * @param facilityReqSeqno The facility request sequence number.
   * @return The facility details as a FacilityDetailRequestDTO
   */
  @PostMapping("/save")
  public Long save(@RequestBody FacilityDetailRequestDTO facilityDetailRequestDTO) {
    log.info("Received: {}", facilityDetailRequestDTO);
    FacilityDetailRequestDTO dto = facilityDetailRequestDTO;
    Long response = facilityDetailService.save(dto);
    return response;
  }

  /**
   * Creates the details for the given facility request sequence number.
   *
   * @param facilityReqSeqno The facility request sequence number.
   * @return The facility details as a FacilityDetailRequestDTO
   */
  @PostMapping("/create")
  public Long createFacility(@RequestBody FacilityDetailRequestDTO facilityReqSeqno) {
    log.info("Received: {}", facilityReqSeqno);
    Long response = facilityDetailService.createFacility(facilityReqSeqno);
    return response;
  }
}