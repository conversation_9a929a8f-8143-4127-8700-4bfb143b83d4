package my.com.cmg.rms.model;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_request_items")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestItemMaster extends BaseEntity {

  @Id
  @Column(name = "item_req_seqno", unique = true, nullable = false)
  @SequenceGenerator(
      name = "request_item_seq_no",
      sequenceName = "rm_request_item_seq",
      allocationSize = 1)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_item_seq_no")
  private Long itemReqSeqno;

  @Column(name = "item_req_code", length = 20)
  private String itemReqCode;

  @Column(name = "item_req_desc", length = 100)
  private String itemReqDesc;

  @Column(name = "item_group_code", length = 20)
  private String itemGroupCode;

  @Column(name = "item_group_desc", length = 100)
  private String itemGroupDesc;

  @Column(name = "generic_name_seqno", length = 100)
  private Long genericNameSeqno;

  @Column(name = "generic_name_code", length = 20)
  private String genericNameCode;

  @Column(name = "generic_name_desc", length = 100)
  private String genericNameDesc;

  @Column(name = "other_active_ingredient", length = 100)
  private String otherActiveIngredient;

  @Column(name = "strength", length = 10)
  private String strength;

  @Column(name = "dosage_seqno", length = 100)
  private Long dosageSeqno;

  @Column(name = "dosage_code", length = 15)
  private String dosageCode;

  @Column(name = "dosage_desc", length = 20)
  private String dosageDesc;

  @Column(name = "item_name", length = 250)
  private String itemName;

  @Column(name = "itm_cat_seqno", length = 100)
  private Long itemCatSeqno;

  @Column(name = "itm_cat_code", length = 6)
  private String itemCatCode;

  @Column(name = "cat_desc", length = 50)
  private String catDesc;

  @Column(name = "item_subgroup_seqno", length = 100)
  private Long itemSubgroupSeqno;

  @Column(name = "itm_subgroup_code", length = 20)
  private String itemSubgroupCode;

  @Column(name = "subgroup_desc", length = 100)
  private String subgroupDesc;

  @Column(name = "freq_seqno", length = 100)
  private Long freqSeqno;

  @Column(name = "freq_code", length = 15)
  private String freqCode;

  @Column(name = "freq_desc", length = 250)
  private String freqDesc;

  @Column(name = "administration_route", length = 100)
  private String administrationRoute;

  @Column(name = "drug_indication", length = 100)
  private String drugIndication;

  @Column(name = "rp_item_type_code", length = 15)
  private String rpItemTypeCode;

  @Column(name = "rp_item_type_desc", length = 50)
  private String rpItemTypeDesc;

  @Column(name = "manufacturing_config", length = 100)
  private String manufacturingConfig;

  @Column(name = "item_packaging_seqno", length = 100)
  private Long itemPackagingSeqno;

  @Column(name = "item_packaging_code", length = 20)
  private String itemPackagingCode;

  @Column(name = "item_packaging_name", length = 200)
  private String itemPackagingName;

  @Column(name = "sku_seqno", length = 100)
  private Long skuSeqno;

  @Column(name = "sku_abbr", length = 10)
  private String skuAbbr;

  @Column(name = "pku_seqno", length = 100)
  private Long pkuSeqno;

  @Column(name = "pku_abbr", length = 10)
  private String pkuAbbr;

  @Column(name = "conversion_factor", length = 10)
  private String conversionFactor;

  @Column(name = "packaging_desc", length = 100)
  private String packagingDesc;

  @Column(name = "mdc_no", length = 200)
  private String mdcNo;

  @Column(name = "product_seqno", length = 100)
  private Long productSeqno;

  @Column(name = "product_code", length = 15)
  private String productCode;

  @Column(name = "product_desc", length = 20)
  private String productDesc;

  @Column(name = "product_name", length = 100)
  private String productName;

  @Column(name = "inovator_type_yn", length = 1)
  private Character inovatorTypeYn;

  @Column(name = "generic_type_yn", length = 1)
  private Character genericTypeYn;

  @Column(name = "manufactured_name", length = 100)
  private String manufacturedName;

  @Column(name = "manufactured_address", length = 200)
  private String manufacturedAddress;

  @Column(name = "importer_name", length = 100)
  private String importerName;

  @Column(name = "importer_address", length = 200)
  private String importerAddress;

  @Column(name = "gtin_no", length = 20)
  private String gtinNo;

  @Column(name = "mda_no", length = 20)
  private String mdaNo;
}
