package my.com.cmg.rms.dto.RequestDetail.SupplierDetail;

import java.math.BigDecimal;
import java.time.LocalDate;

public record SupplierDetailRequestDTO(
    Long supplierReqSeqno,
    String supplierReqName,
    String companyRegNo,
    LocalDate regExpiryDate,
    String trsRegNo,
    String companyStatus,
    String address1,
    String address2,
    String address3,
    String city,
    String postcode,
    String state,
    String country,
    String mobilePhone,
    String email,
    String contactPerson,
    String contactNo,
    String supplierReqCode,
    String supplierReqType,
    String supplierReqTypeDesc,
    String companyStatusDesc,
    String stateDesc,
    String parameter1,
    String parameter2,
    BigDecimal parameter3,
    BigDecimal parameter4,
    LocalDate parameter5,
    String activeFlag,
    Long createdBy,
    Long updatedBy) {}
