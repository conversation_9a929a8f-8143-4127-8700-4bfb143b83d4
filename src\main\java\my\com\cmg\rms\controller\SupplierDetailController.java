package my.com.cmg.rms.controller;

import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDetail.SupplierDetail.SupplierDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.SupplierDetail.SupplierDetailRequestDTO;
import my.com.cmg.rms.service.ISupplierDetailService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/rms/supplier_detail")
public class SupplierDetailController {
  private final ISupplierDetailService supplierDetailService;

  public SupplierDetailController(final ISupplierDetailService supplierDetailService) {
    this.supplierDetailService = supplierDetailService;
  }

  /**
   * Retrieves the details for the given supplier request sequence number.
   *
   * @param supplierReqSeqno The supplier request sequence number.
   * @return The supplier details as a SupplierDetailDTO
   */
  @GetMapping("/detail/{supplierReqSeqno}")
  public SupplierDetailDTO getSupplierDetail(
      @PathVariable("supplierReqSeqno") Long supplierReqSeqno) {
    SupplierDetailDTO response = supplierDetailService.getSupplierDetail(supplierReqSeqno);

    return response;
  }

  /**
   * Save the details for the given supplier request sequence number.
   *
   * @param supplierReqSeqno The supplier request sequence number.
   * @return The supplier details as a SupplierDetailRequestDTO
   */
  @PostMapping("/save")
  public Long save(@RequestBody SupplierDetailRequestDTO supplierDetailRequestDTO) {
    log.info("Received: {}", supplierDetailRequestDTO);
    Long response = supplierDetailService.save(supplierDetailRequestDTO);
    return response;
  }

  /**
   * Creates the details for the given supplier request sequence number.
   *
   * @param supplierReqSeqno The supplier request sequence number.
   * @return The supplier details as a SupplierDetailRequestDTO
   */
  @PostMapping("/create")
  public Long createSupplier(@RequestBody SupplierDetailRequestDTO supplierReqSeqno) {
    log.info("Received: {}", supplierReqSeqno);
    Long response = supplierDetailService.createSupplier(supplierReqSeqno);
    return response;
  }
}