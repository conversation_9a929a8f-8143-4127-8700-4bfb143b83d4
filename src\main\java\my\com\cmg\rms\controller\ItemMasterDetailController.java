package my.com.cmg.rms.controller;

import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDetail.ItemMasterDetail.ItemMasterDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.ItemMasterDetail.ItemMasterDetailRequestDTO;
import my.com.cmg.rms.service.IItemMasterDetailService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/v1/rms/item_master_detail")
public class ItemMasterDetailController {
  private final IItemMasterDetailService itemMasterDetailService;

  public ItemMasterDetailController(final IItemMasterDetailService itemMasterDetailService) {
    this.itemMasterDetailService = itemMasterDetailService;
  }

  /**
   * Retrieves the details for the given item master request sequence number.
   *
   * @param itemMasterReqSeqno The item master request sequence number.
   * @return The item master details as a ItemMasterDetailDTO
   */
  @GetMapping("/detail/{itemMasterReqSeqno}")
  public ItemMasterDetailDTO getItemMasterDetail(
      @PathVariable("itemMasterReqSeqno") Long itemMasterReqSeqno) {
    ItemMasterDetailDTO response = itemMasterDetailService.getItemMasterDetail(itemMasterReqSeqno);

    return response;
  }

  /**
   * Save the details for the given item master request sequence number.
   *
   * @param itemMasterReqSeqno The item master request sequence number.
   * @return The item master details as a ItemMasterDetailRequestDTO
   */
  @PostMapping("/save")
  public Long save(@RequestBody ItemMasterDetailRequestDTO itemMasterDetailRequestDTO) {
    log.info("Received: {}", itemMasterDetailRequestDTO);
    Long response = itemMasterDetailService.save(itemMasterDetailRequestDTO);
    return response;
  }

  /**
   * Creates the details for the given item master request sequence number.
   *
   * @param itemMasterReqSeqno The item master request sequence number.
   * @return The item master details as a ItemMasterDetailRequestDTO
   */
  @PostMapping("/create")
  public Long createItemMaster(@RequestBody ItemMasterDetailRequestDTO itemMasterReqSeqno) {
    log.info("Received: {}", itemMasterReqSeqno);
    Long response = itemMasterDetailService.createItemMaster(itemMasterReqSeqno);
    return response;
  }
}