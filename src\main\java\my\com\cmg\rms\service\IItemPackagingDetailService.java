package my.com.cmg.rms.service;

import my.com.cmg.rms.dto.RequestDetail.ItemPackagingDetail.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.ItemPackagingDetail.ItemPackagingDetailRequestDTO;

public interface IItemPackagingDetailService {

  ItemPackagingDetailDTO getItemPackagingDetail(Long itemPackagingReqSeqno);

  Long save(ItemPackagingDetailRequestDTO itemPackagingDetailRequestDTO);

  Long createItemPackaging(ItemPackagingDetailRequestDTO itemPackagingDetailRequestDTO);
}