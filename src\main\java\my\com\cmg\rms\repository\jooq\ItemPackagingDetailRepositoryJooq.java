package my.com.cmg.rms.repository.jooq;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.noCondition;

import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.RequestDetail.ItemPackagingDetail.ItemPackagingDetailDTO;
import my.com.cmg.rms.utils.LogUtil;
import my.com.cmg.rms.utils.TableUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record18;
import org.jooq.Select;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class ItemPackagingDetailRepositoryJooq {

  private final DSLContext dsl;

  public ItemPackagingDetailRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  public ItemPackagingDetailDTO getItemPackagingDetail(Long transSeqno) {

    // condition
    Condition condition = noCondition();
    condition = condition.and(field("item_packaging_req_seqno").eq(transSeqno));

    // fields from ITEM_PACKAGING
    Field<Long> itemPackagingReqSeqno =
        field("item_packaging_req_seqno", Long.class).as("itemPackagingReqSeqno");
    Field<Long> itemSeqno = field("item_seqno", Long.class).as("itemSeqno");
    Field<String> itemName = field("item_name", String.class).as("itemName");
    Field<String> itemCode = field("item_code", String.class).as("itemCode");
    Field<String> itemPackagingName =
        field("item_packaging_name", String.class).as("itemPackagingName");
    Field<Long> skuSeqno = field("sku_seqno", Long.class).as("skuSeqno");
    Field<Long> pkuSeqno = field("pku_seqno", Long.class).as("pkuSeqno");
    Field<BigDecimal> conversionFactor =
        field("conversion_factor", BigDecimal.class).as("conversionFactor");
    Field<String> packagingDesc = field("packaging_desc", String.class).as("packagingDesc");
    Field<String> productList = field("product_list", String.class).as("productList");
    Field<Long> productSeqno = field("product_seqno", Long.class).as("productSeqno");
    Field<String> productName = field("product_name", String.class).as("productName");
    Field<String> manufacturedName =
        field("manufactured_name", String.class).as("manufacturedName");
    Field<String> importerName = field("importer_name", String.class).as("importerName");
    Field<String> manufacturedAddress =
        field("manufactured_address", String.class).as("manufacturedAddress");
    Field<String> importerAddress = field("importer_address", String.class).as("importerAddress");
    Field<String> gtinNo = field("gtin_no", String.class).as("gtinNo");
    Field<String> mdaNo = field("mda_no", String.class).as("mdaNo");

    Select<
            Record18<
                Long,
                Long,
                String,
                String,
                String,
                Long,
                Long,
                BigDecimal,
                String,
                String,
                Long,
                String,
                String,
                String,
                String,
                String,
                String,
                String>>
        query =
            dsl.select(
                    itemPackagingReqSeqno,
                    itemSeqno,
                    itemName,
                    itemCode,
                    itemPackagingName,
                    skuSeqno,
                    pkuSeqno,
                    conversionFactor,
                    packagingDesc,
                    productList,
                    productSeqno,
                    productName,
                    manufacturedName,
                    importerName,
                    manufacturedAddress,
                    importerAddress,
                    gtinNo,
                    mdaNo)
                .from(TableUtil.table(TableUtil.RM_REQUEST_ITEM_PACKAGING, "ITM"))
                .where(condition)
                .limit(1);

    log.info(LogUtil.QUERY, query);
    ItemPackagingDetailDTO result = query.fetchOneInto(ItemPackagingDetailDTO.class);

    return result;
  }
}
