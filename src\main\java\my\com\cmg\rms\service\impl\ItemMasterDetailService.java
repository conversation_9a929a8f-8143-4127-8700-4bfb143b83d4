package my.com.cmg.rms.service.impl;

import jakarta.persistence.EntityNotFoundException;
import java.time.LocalDateTime;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.RequestDetail.ItemMasterDetail.ItemMasterDetailDTO;
import my.com.cmg.rms.dto.RequestDetail.ItemMasterDetail.ItemMasterDetailRequestDTO;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;
import my.com.cmg.rms.model.RequestItemMaster;
import my.com.cmg.rms.repository.jooq.ItemMasterDetailRepositoryJooq;
import my.com.cmg.rms.repository.jpa.ItemMasterDetailRepository;
import my.com.cmg.rms.service.IItemMasterDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ItemMasterDetailService implements IItemMasterDetailService {

  private final ItemMasterDetailRepository requestItemMasterRepository;
  private final ItemMasterDetailRepositoryJooq itemMasterDetailRepositoryJooq;

  public ItemMasterDetailService(
      final ItemMasterDetailRepository requestItemMasterRepository,
      final ItemMasterDetailRepositoryJooq itemMasterDetailRepositoryJooq) {
    this.requestItemMasterRepository = requestItemMasterRepository;
    this.itemMasterDetailRepositoryJooq = itemMasterDetailRepositoryJooq;
  }

  @Override
  public ItemMasterDetailDTO getItemMasterDetail(Long itemMasterReqSeqno) {
    return itemMasterDetailRepositoryJooq.getItemMasterDetail(itemMasterReqSeqno);
  }

  @Override
  @Transactional
  public Long createItemMaster(ItemMasterDetailRequestDTO itemMasterDetailRequestDTO) {

    RequestItemMaster itemMaster = new RequestItemMaster();

    updateItemMasterFromDTO(itemMaster, itemMasterDetailRequestDTO);

    itemMaster.setCreatedBy(1L);
    itemMaster.setUpdatedBy(1L);
    itemMaster.setCreatedDate(LocalDateTime.now());
    itemMaster.setUpdatedDate(LocalDateTime.now());
    itemMaster.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    RequestItemMaster savedItemMaster = requestItemMasterRepository.save(itemMaster);

    return savedItemMaster.getItemReqSeqno();
  }

  @Override
  @Transactional
  public Long save(ItemMasterDetailRequestDTO itemMasterDetailRequestDTO) {
    if (itemMasterDetailRequestDTO.itemReqSeqno() == null) {
      throw new RmsException(ExceptionCode.BAD_REQUEST, "itemReqSeqno is required for update");
    }

    RequestItemMaster itemMaster =
        requestItemMasterRepository
            .findById(itemMasterDetailRequestDTO.itemReqSeqno())
            .orElseThrow(() -> new EntityNotFoundException("Item master not found"));

    updateItemMasterFromDTO(itemMaster, itemMasterDetailRequestDTO);
    RequestItemMaster savedItemMaster = requestItemMasterRepository.save(itemMaster);
    return savedItemMaster.getItemReqSeqno();
  }

  private void updateItemMasterFromDTO(
      RequestItemMaster itemMaster, ItemMasterDetailRequestDTO dto) {
    itemMaster.setItemGroupCode(dto.itemGroupCode());
    itemMaster.setGenericNameSeqno(dto.genericNameSeqno());
    itemMaster.setOtherActiveIngredient(dto.otherActiveIngredient());
    itemMaster.setStrength(dto.strength());
    itemMaster.setDosageSeqno(dto.dosageSeqno());
    itemMaster.setItemName(dto.itemName());
    itemMaster.setItemCatSeqno(dto.itemCatSeqno());
    itemMaster.setItemSubgroupSeqno(dto.itemSubgroupSeqno());
    itemMaster.setFreqSeqno(dto.freqSeqno());
    itemMaster.setAdministrationRoute(dto.administrationRoute());
    itemMaster.setDrugIndication(dto.drugIndication());
    itemMaster.setRpItemTypeCode(dto.rpItemTypeCode());
    itemMaster.setItemPackagingSeqno(dto.itemPackagingSeqno());
    itemMaster.setSkuSeqno(dto.skuSeqno());
    itemMaster.setPkuSeqno(dto.pkuSeqno());
    itemMaster.setConversionFactor(dto.conversionFactor());
    itemMaster.setPackagingDesc(dto.packagingDesc());
    itemMaster.setMdcNo(dto.mdcNo());
    itemMaster.setProductSeqno(dto.productSeqno());
    itemMaster.setProductName(dto.productName());
    itemMaster.setManufacturedName(dto.manufacturedName());
    itemMaster.setImporterName(dto.importerName());
    itemMaster.setManufacturedAddress(dto.manufacturedAddress());
    itemMaster.setImporterAddress(dto.importerAddress());
    itemMaster.setGtinNo(dto.gtinNo());
    itemMaster.setMdaNo(dto.mdaNo());
  }
}
